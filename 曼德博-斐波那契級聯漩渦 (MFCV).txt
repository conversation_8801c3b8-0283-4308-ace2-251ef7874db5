//@version=5
indicator("曼德博-斐波那契級聯漩渦 (MFCV)", overlay=true, max_labels_count=500, max_lines_count=500)

// ========================================
// 詳細輸入系統
// ========================================
group_mandelbrot = "🌊 曼德博級聯參數"
cascade_depth = input.int(5, "級聯層數", minval=3, maxval=8, group=group_mandelbrot, tooltip="波動帶層數 (3-4:簡潔 | 5-6:平衡 | 7-8:詳細) • 加密貨幣:5-7 | 股指:4-5 | 外匯:3-4 • 更高時間框架=更多層級")
hurst_period = input.int(100, "赫斯特指數週期", minval=20, maxval=200, group=group_mandelbrot, tooltip="趨勢持續性回顧週期 • 加密貨幣:50-100 | 股指:80-120 | 外匯:100-150 • 短線:20-50 | 波段:100-150")
volatility_cascade_factor = input.float(1.618, "級聯比率", minval=1.0, maxval=3.0, step=0.1, group=group_mandelbrot, tooltip="波動帶寬度乘數 • 加密貨幣:1.618-2.618 | 股指:1.382-1.618 | 外匯:1.0-1.382 • 趨勢市場:較高 | 震盪市場:較低")
fractal_memory = input.int(89, "分形記憶週期", minval=21, maxval=233, group=group_mandelbrot, tooltip="斐波回顧週期 • 加密貨幣:34-55 | 股指:55-89 | 外匯:89-144 • 使用斐波數列:21,34,55,89,144,233")
group_fibonacci = "🌀 斐波那契漩渦設置"
spiral_periods = input.string("8,13,21,34,55", "螺旋週期", group=group_fibonacci, tooltip="斐波螺旋層級 • 快速:'5,8,13,21,34' | 標準:'8,13,21,34,55' | 波段:'13,21,34,55,89' • 加密貨幣:用更快週期")
vortex_rotation_speed = input.float(0.618, "旋轉速度", minval=0.1, maxval=2.0, step=0.1, group=group_fibonacci, tooltip="螺旋振盪速度 • 加密貨幣:0.786-1.2 | 股指:0.5-0.786 | 外匯:0.382-0.618 • 值越高=信號越多")
volume_amplification = input.bool(true, "成交量放大", group=group_fibonacci, tooltip="基於成交量的螺旋擴張 • 啟用:股票,加密貨幣,期指 • 禁用:外匯,隔夜交易")
fib_update_bars = input.int(50, "斐波更新頻率", minval=10, maxval=200, group=group_fibonacci, tooltip="斐波水平更新間隔 • 值越低=更敏感 • 值越高=更穩定")
group_visual = "🎨 視覺設置"
show_cascade_bands = input.bool(true, "顯示級聯帶", group=group_visual,tooltip="顯示/隱藏波動帶 • 圖表混亂時禁用")
show_vortex_spirals = input.bool(true, "顯示漩渦螺旋", group=group_visual, tooltip="顯示/隱藏斐波螺旋 • 簡化圖表時禁用")
show_fibonacci_levels = input.bool(true, "顯示斐波水平", group=group_visual, tooltip="顯示斐波回撤位(23.6%,38.2%,50%,61.8%,78.6%)")
show_confluence_zones = input.bool(true, "高亮匯合區", group=group_visual, tooltip="高亮漩渦/級聯交匯區 • 最佳支撐阻力區")
cascade_transparency = input.int(85, "帶狀透明度", minval=50, maxval=95, group=group_visual, tooltip="帶狀透明度(50-70:明顯 | 70-85:平衡 | 85-95:淡雅)")
vortex_style = input.string("圓圈", "漩渦顯示樣式", options=["線條", "圓圈", "十字"], group=group_visual, tooltip="螺旋視覺樣式 • 線條:平滑 | 圓圈:清晰點位 | 十字:精確")
group_dashboard = "📊 儀表板設置"
show_main_dashboard = input.bool(true, "分形儀表板", group=group_dashboard, tooltip="顯示/隱藏主指標儀表板")
show_vortex_metrics = input.bool(true, "漩渦指標", group=group_dashboard, tooltip="顯示/隱藏漩渦偏差面板")
show_theory_guide = input.bool(true, "理論指南", group=group_dashboard, tooltip="顯示/隱藏教育理論面板")
dashboard_size = input.string("標準", "儀表板尺寸", options=["小", "標準", "大"], group=group_dashboard, tooltip="儀表板文字大小")
group_colors = "🎨 顏色主題"
color_scheme = input.string("分形", "顏色主題", options=["分形", "黃金", "等離子", "宇宙", "矩陣", "火焰"], group=group_colors, tooltip="視覺顏色主題 • 分形:平衡 | 黃金:暖色 | 等離子:鮮豔 | 宇宙:暗色 | 矩陣:綠色 | 火焰:熱力圖")

// ========================================
// 顏色系統
// ========================================
get_theme_colors() =>
    switch color_scheme
        "分形" => [#4A90E2, #9B59B6, #F39C12, #E74C3C, #1ABC9C, #34495E]
        "黃金" => [#FFD700, #FFA500, #FF8C00, #FF6347, #FFB347, #FFDB58]
        "等離子" => [#FF006E, #FB5607, #FFBE0B, #8338EC, #3A86FF, #FF4365]
        "宇宙" => [#00D9FF, #00A8CC, #0080FF, #4169E1, #6A5ACD, #9370DB]
        "矩陣" => [#00FF41, #008F11, #003B00, #00FF41, #00D100, #00A100]
        "火焰" => [#FF0000, #FF4500, #FF6347, #FF8C00, #FFA500, #FFD700]
        => [#4A90E2, #9B59B6, #F39C12, #E74C3C, #1ABC9C, #34495E]

[col_primary, col_secondary, col_accent, col_warning, col_danger, col_info] = get_theme_colors()

// ========================================
// 解析斐波那契週期
// ========================================
parse_fibonacci_periods(input_string) =>
    parts = str.split(input_string, ",")
    periods = array.new<int>()
    for part in parts
        if str.length(str.trim(part)) > 0
            array.push(periods, int(str.tonumber(str.trim(part))))
    periods

fib_periods = parse_fibonacci_periods(spiral_periods)

// ========================================
// 曼德博計算（動態響應）
// ========================================
calculate_hurst_exponent(src, length) =>
    mean = ta.sma(src, length)
    cumsum = 0.0, max_cumsum = 0.0, min_cumsum = 0.0
    
    for i = 0 to length - 1
        cumsum := cumsum + (src[i] - mean)
        max_cumsum := math.max(max_cumsum, cumsum)
        min_cumsum := math.min(min_cumsum, cumsum)
    
    R = max_cumsum - min_cumsum
    S = ta.stdev(src, length)
    RS = S != 0 ? R / S : 0
    H = RS > 0 ? math.log(RS) / math.log(length / 2) : 0.5
    math.max(0, math.min(1, H))

// 動態週期計算
hurst = calculate_hurst_exponent(close, hurst_period)
fractal_dimension = 2 - hurst

// ========================================
// 波動級聯（可視化效果）
// ========================================
base_volatility = ta.atr(20)
cascade_volatilities = array.new<float>()

// 創建遞增波動的級聯層級
for i = 0 to cascade_depth - 1
    cascade_vol = base_volatility * math.pow(volatility_cascade_factor, i)
    array.push(cascade_volatilities, cascade_vol)

// ======================================== 
// 動態漩渦計算 
// ======================================== 
calculate_vortex_value(src, index, speed, vol_amp, period) => 
    base_movement = math.sin((index * speed) / period) * ta.atr(period)
    volume_factor = vol_amp ? 1 + (volume / ta.sma(volume, 50) - 1) * 0.5 : 1
    src + base_movement * volume_factor

// 使用固定週期計算漩渦值
vortex_8 = calculate_vortex_value(close, bar_index, vortex_rotation_speed, volume_amplification, 8)
vortex_13 = calculate_vortex_value(close, bar_index, vortex_rotation_speed, volume_amplification, 13)
vortex_21 = calculate_vortex_value(close, bar_index, vortex_rotation_speed, volume_amplification, 21)
vortex_34 = calculate_vortex_value(close, bar_index, vortex_rotation_speed, volume_amplification, 34)
vortex_55 = calculate_vortex_value(close, bar_index, vortex_rotation_speed, volume_amplification, 55)

// 儲存顯示用週期值
period_0 = array.size(fib_periods) > 0 ? array.get(fib_periods, 0) : 8
period_1 = array.size(fib_periods) > 1 ? array.get(fib_periods, 1) : 13
period_2 = array.size(fib_periods) > 2 ? array.get(fib_periods, 2) : 21
period_3 = array.size(fib_periods) > 3 ? array.get(fib_periods, 3) : 34
period_4 = array.size(fib_periods) > 4 ? array.get(fib_periods, 4) : 55

// 存入數組（明確浮點類型）
vortex_values = array.new<float>()
array.push(vortex_values, vortex_8)
array.push(vortex_values, vortex_13)
array.push(vortex_values, vortex_21)
array.push(vortex_values, vortex_34)
array.push(vortex_values, vortex_55)

// ========================================
// 信號生成
// ========================================
// 基於輸入的動態閾值
hurst_threshold = 0.5 + (hurst_period - 100) * 0.001
volatility_expansion = base_volatility > ta.sma(base_volatility, 50) * (1 + (cascade_depth - 5) * 0.05)

// 市場狀態變量
trend_persistence = hurst > 0.65
mean_reversion_zone = hurst < 0.35

// 漩渦對齊檢查（分形寬鬆版）
vortex_bullish = vortex_8 > close and vortex_13 > close
vortex_bearish = vortex_8 < close and vortex_13 < close

// 計算趨勢強度
trend_strength = math.abs(hurst - 0.5) * 2

// 預計算指標確保一致性
rsi_value = ta.rsi(close, 14)
sma_50 = ta.sma(close, 50)

// 添加信號冷卻防止過度
var int last_fractal_signal = 0
var int last_cascade_signal = 0
signal_cooldown = 15  // 從20減少

// 更嚴格的信號條件
fractal_signal = false
cascade_signal = false

// 分形信號 - 較寬鬆
// 當趨勢中等強或波動擴張伴隨漩渦確認時觸發
if (bar_index - last_fractal_signal) > signal_cooldown
    if (hurst > 0.65 and volatility_expansion) or 
       (hurst < 0.35 and rsi_value < 35) or
       (trend_strength > 0.4 and vortex_bullish and rsi_value < 70)
        fractal_signal := true
        last_fractal_signal := bar_index

// 級聯信號 - 更合理條件
// 在趨勢耗盡或強反轉時觸發
if (bar_index - last_cascade_signal) > signal_cooldown
    if (hurst > 0.6 and close > sma_50 and rsi_value > 65 and vortex_bearish) or
       (hurst < 0.4 and close < sma_50 and rsi_value < 35 and vortex_bullish) or
       (volatility_expansion and trend_strength > 0.6 and math.abs(close - sma_50) / sma_50 > 0.02)
        cascade_signal := true
        last_cascade_signal := bar_index

// ========================================
// 級聯帶可視化
// ========================================
// 預定義標題常量
upper_0_title = "上軌 0"
upper_1_title = "上軌 1"
upper_2_title = "上軌 2"
upper_3_title = "上軌 3"
upper_4_title = "上軌 4"

lower_0_title = "下軌 0"
lower_1_title = "下軌 1"
lower_2_title = "下軌 2"
lower_3_title = "下軌 3"
lower_4_title = "下軌 4"

// 計算級聯水平
cascade_upper_0 = show_cascade_bands and array.size(cascade_volatilities) > 0 ? close + array.get(cascade_volatilities, 0) : na
cascade_lower_0 = show_cascade_bands and array.size(cascade_volatilities) > 0 ? close - array.get(cascade_volatilities, 0) : na
cascade_upper_1 = show_cascade_bands and array.size(cascade_volatilities) > 1 ? close + array.get(cascade_volatilities, 1) : na
cascade_lower_1 = show_cascade_bands and array.size(cascade_volatilities) > 1 ? close - array.get(cascade_volatilities, 1) : na
cascade_upper_2 = show_cascade_bands and array.size(cascade_volatilities) > 2 ? close + array.get(cascade_volatilities, 2) : na
cascade_lower_2 = show_cascade_bands and array.size(cascade_volatilities) > 2 ? close - array.get(cascade_volatilities, 2) : na
cascade_upper_3 = show_cascade_bands and array.size(cascade_volatilities) > 3 ? close + array.get(cascade_volatilities, 3) : na
cascade_lower_3 = show_cascade_bands and array.size(cascade_volatilities) > 3 ? close - array.get(cascade_volatilities, 3) : na
cascade_upper_4 = show_cascade_bands and array.size(cascade_volatilities) > 4 ? close + array.get(cascade_volatilities, 4) : na
cascade_lower_4 = show_cascade_bands and array.size(cascade_volatilities) > 4 ? close - array.get(cascade_volatilities, 4) : na

// 各層級顏色
cascade_color_0 = color.from_gradient(0, 0, 7, col_primary, col_secondary)
cascade_color_1 = color.from_gradient(1, 0, 7, col_primary, col_secondary)
cascade_color_2 = color.from_gradient(2, 0, 7, col_primary, col_secondary)
cascade_color_3 = color.from_gradient(3, 0, 7, col_primary, col_secondary)
cascade_color_4 = color.from_gradient(4, 0, 7, col_primary, col_secondary)

// 繪製級聯帶
p_upper_0 = plot(cascade_upper_0, upper_0_title, color=color.new(cascade_color_0, cascade_transparency), linewidth=1)
p_lower_0 = plot(cascade_lower_0, lower_0_title, color=color.new(cascade_color_0, cascade_transparency), linewidth=1)
p_upper_1 = plot(cascade_upper_1, upper_1_title, color=color.new(cascade_color_1, cascade_transparency), linewidth=1)
p_lower_1 = plot(cascade_lower_1, lower_1_title, color=color.new(cascade_color_1, cascade_transparency), linewidth=1)
p_upper_2 = plot(cascade_upper_2, upper_2_title, color=color.new(cascade_color_2, cascade_transparency), linewidth=1)
p_lower_2 = plot(cascade_lower_2, lower_2_title, color=color.new(cascade_color_2, cascade_transparency), linewidth=1)
p_upper_3 = plot(cascade_upper_3, upper_3_title, color=color.new(cascade_color_3, cascade_transparency), linewidth=1)
p_lower_3 = plot(cascade_lower_3, lower_3_title, color=color.new(cascade_color_3, cascade_transparency), linewidth=1)
p_upper_4 = plot(cascade_upper_4, upper_4_title, color=color.new(cascade_color_4, cascade_transparency), linewidth=1)
p_lower_4 = plot(cascade_lower_4, lower_4_title, color=color.new(cascade_color_4, cascade_transparency), linewidth=1)

// 帶間填充
fill(p_upper_0, p_lower_0, color=color.new(cascade_color_0, cascade_transparency + 10))
fill(p_upper_1, p_lower_1, color=color.new(cascade_color_1, cascade_transparency + 10))
fill(p_upper_2, p_lower_2, color=color.new(cascade_color_2, cascade_transparency + 10))
fill(p_upper_3, p_lower_3, color=color.new(cascade_color_3, cascade_transparency + 10))
fill(p_upper_4, p_lower_4, color=color.new(cascade_color_4, cascade_transparency + 10))

// ========================================
// 漩渦螺旋（帶樣式選項）
// ========================================
// 定義標題常量
vortex_0_title = "漩渦 0"
vortex_1_title = "漩渦 1"
vortex_2_title = "漩渦 2"
vortex_3_title = "漩渦 3"
vortex_4_title = "漩渦 4"

// 確定繪圖樣式
plot_style = vortex_style == "線條" ? plot.style_line : vortex_style == "十字" ? plot.style_cross : plot.style_circles

// 確定顏色
vortex_color_0 = show_vortex_spirals ? (vortex_8 > close ? color.new(col_primary, 70) : color.new(col_danger, 70)) : na
vortex_color_1 = show_vortex_spirals ? (vortex_13 > close ? color.new(col_primary, 70) : color.new(col_danger, 70)) : na
vortex_color_2 = show_vortex_spirals ? (vortex_21 > close ? color.new(col_primary, 70) : color.new(col_danger, 70)) : na
vortex_color_3 = show_vortex_spirals ? (vortex_34 > close ? color.new(col_primary, 70) : color.new(col_danger, 70)) : na
vortex_color_4 = show_vortex_spirals ? (vortex_55 > close ? color.new(col_primary, 70) : color.new(col_danger, 70)) : na

// 繪製漩渦螺旋（必須在全局作用域）
plot(show_vortex_spirals ? vortex_8 : na, vortex_0_title, color=vortex_color_0, style=plot_style, linewidth=2)
plot(show_vortex_spirals ? vortex_13 : na, vortex_1_title, color=vortex_color_1, style=plot_style, linewidth=2)
plot(show_vortex_spirals ? vortex_21 : na, vortex_2_title, color=vortex_color_2, style=plot_style, linewidth=2)
plot(show_vortex_spirals ? vortex_34 : na, vortex_3_title, color=vortex_color_3, style=plot_style, linewidth=2)
plot(show_vortex_spirals ? vortex_55 : na, vortex_4_title, color=vortex_color_4, style=plot_style, linewidth=2)

// ========================================
// 斐波那契水平（預計算）
// ========================================
// 儲存線和標籤引用
var line[] fib_lines = array.new<line>()
var label[] fib_labels = array.new<label>()

// 每根K線計算確保一致性
fib_highest = ta.highest(high, fractal_memory)
fib_lowest = ta.lowest(low, fractal_memory)
fib_sma = ta.sma(close, 50)

// 智能更新控制 - 僅當價格遠離當前水平時更新
var float stored_fib_highest = fib_highest
var float stored_fib_lowest = fib_lowest
price_near_fib_level = false

// 檢查價格是否接近當前斐波水平0.5%範圍內
if array.size(fib_lines) > 0
    fib_range = stored_fib_highest - stored_fib_lowest
    for level in array.from(0.786, 0.618, 0.5, 0.382, 0.236)
        fib_price = close > fib_sma ? stored_fib_highest - fib_range * level : stored_fib_lowest + fib_range * level
        if math.abs(close - fib_price) / close < 0.005  // 0.5%範圍內
            price_near_fib_level := true

// 動態更新控制
volatility_spike = base_volatility > ta.sma(base_volatility, 20) * 1.5
var int last_fib_update = 0
bars_since_last_update = bar_index - last_fib_update

// 僅當價格不接近水平或範圍顯著變化時更新
range_change = math.abs(fib_highest - stored_fib_highest) / stored_fib_highest > 0.02 or math.abs(fib_lowest - stored_fib_lowest) / stored_fib_lowest > 0.02
should_update_fibs = barstate.islast and not price_near_fib_level and (bars_since_last_update >= fib_update_bars or volatility_spike or range_change)

// ========================================
// 斐波那契水平繪製
// ========================================
if show_fibonacci_levels and should_update_fibs
    // 清除舊線和標籤（帶邊界檢查）
    if array.size(fib_lines) > 0
        for i = array.size(fib_lines) - 1 to 0
            line.delete(array.get(fib_lines, i))
        array.clear(fib_lines)
    
    if array.size(fib_labels) > 0
        for i = array.size(fib_labels) - 1 to 0
            label.delete(array.get(fib_labels, i))
        array.clear(fib_labels)
    
    // 更新存儲值和最後更新K線
    stored_fib_highest := fib_highest
    stored_fib_lowest := fib_lowest
    last_fib_update := bar_index
    
    // 計算新水平
    price_range = fib_highest - fib_lowest
    
    fib_levels = array.from(0.236, 0.382, 0.5, 0.618, 0.786)
    fib_colors = array.from(col_info, col_primary, col_accent, #FFD700, col_warning)
    
    // 繪製新線（帶距離透明度）
    for i = 0 to array.size(fib_levels) - 1
        level = array.get(fib_levels, i)
        price = close > fib_sma ? fib_highest - price_range * level : fib_lowest + price_range * level
        
        // 計算與當前價格距離以確定透明度
        distance_from_price = math.abs(close - price) / close
        // 越近的線越明顯（基於距離的30-80透明度）
        line_transparency = 30 + math.min(50, distance_from_price * 1000)
        
        // 創建向右無限延伸的線
        new_line = line.new(bar_index - 20, price, bar_index + 10, price, color=color.new(array.get(fib_colors, i), line_transparency), width=2, extend=extend.right)
        
        // 標籤也帶距離透明度
        label_transparency = 60 + math.min(30, distance_from_price * 500)
        
        // 創建標籤
        new_label = label.new(bar_index + 5, price, str.tostring(level * 100, "#.#") + "%", color=color.new(array.get(fib_colors, i), label_transparency), textcolor=array.get(fib_colors, i), style=label.style_label_left, size=size.small)
        
        // 儲存引用
        array.push(fib_lines, new_line)
        array.push(fib_labels, new_label)
        
// ========================================
// 匯合區（預計算）
// ========================================
// 每根K線計算ATR
confluence_atr = ta.atr(20)

// ========================================
// 匯合區繪製
// ========================================
if show_confluence_zones and barstate.islast
    // 尋找指標對齊區域
    if array.size(cascade_volatilities) > 0
        // 直接從漩渦值計算平均值
        avg_vortex = (vortex_8 + vortex_13 + vortex_21 + vortex_34 + vortex_55) / 5
        first_cascade = close + array.get(cascade_volatilities, 0)
        
        if math.abs(avg_vortex - first_cascade) < confluence_atr * 0.5
            box.new(bar_index - 10, math.max(avg_vortex, first_cascade), bar_index + 5, math.min(avg_vortex, first_cascade), bgcolor=color.new(col_accent, 90), border_color=color.new(col_accent, 50))

// ========================================
// 信號標籤
// ========================================
if fractal_signal
    label.new(bar_index, low * 0.995, "🌀 分形信號", color=col_primary, style=label.style_label_up, textcolor=color.white, size=size.small)

if cascade_signal
    label.new(bar_index, high * 1.005, "🌊 級聯信號", color=col_danger, style=label.style_label_down, textcolor=color.white, size=size.small)

// ========================================
// 儀表板輔助
// ========================================
get_text_size() =>
    dashboard_size == "小" ? size.tiny : dashboard_size == "大" ? size.normal : size.small

// ========================================
// 動態儀表板
// ========================================
if show_main_dashboard
    var table main_dash = table.new(position.top_right, 2, 8, bgcolor=#000000, border_color=#333333, border_width=1)
    
    if barstate.islast
        // 獲取尺寸
        text_size = get_text_size()
        
        // 更新標題單元格
        table.cell(main_dash, 0, 0, "🌀 曼德博-斐波那契", text_color=col_primary, text_size=text_size, bgcolor=#000000)
        table.cell(main_dash, 1, 0, "級聯漩渦 🌊", text_color=col_secondary, text_size=text_size, bgcolor=#000000)
        
        // 帶動態解讀的赫斯特指數
        hurst_state = hurst > 0.65 ? "強趨勢" : hurst > 0.55 ? "趨勢中" : hurst > 0.45 ? "隨機波動" : "均值回歸"
        table.cell(main_dash, 0, 1, "赫斯特指數", text_color=#808080, text_size=text_size, bgcolor=#000000)
        table.cell(main_dash, 1, 1, str.tostring(hurst, "#.###") + " " + hurst_state, text_color=hurst > 0.6 ? col_danger : hurst < 0.4 ? col_primary : col_warning, text_size=text_size, bgcolor=#000000)
        
        // 帶含義的分形維度
        complexity_level = fractal_dimension > 1.5 ? "混沌" : fractal_dimension > 1.3 ? "複雜" : "簡單"
        table.cell(main_dash, 0, 2, "分形維度", text_color=#808080, text_size=text_size, bgcolor=#000000)
        table.cell(main_dash, 1, 2, str.tostring(fractal_dimension, "#.###") + " " + complexity_level, text_color=col_accent, text_size=text_size, bgcolor=#000000)
        
        // 波動級聯狀態
        cascade_status = volatility_expansion ? "擴張中" : "收縮中"
        table.cell(main_dash, 0, 3, "波動級聯", text_color=#808080, text_size=text_size, bgcolor=#000000)
        table.cell(main_dash, 1, 3, cascade_status, text_color=volatility_expansion ? col_danger : col_primary, text_size=text_size, bgcolor=#000000)
        
        // 漩渦旋轉影響
        rotation_impact = vortex_rotation_speed > 1.0 ? "快速" : vortex_rotation_speed > 0.5 ? "中等" : "慢速"
        table.cell(main_dash, 0, 4, "漩渦速度", text_color=#808080, text_size=text_size, bgcolor=#000000)
        table.cell(main_dash, 1, 4, rotation_impact + " (" + str.tostring(vortex_rotation_speed, "#.##") + ")", text_color=col_warning, text_size=text_size, bgcolor=#000000)
        
        // 市場狀態
        market_state = trend_strength > 0.7 ? "強方向性" : trend_strength > 0.3 ? "中等趨勢" : "震盪/區間"
        table.cell(main_dash, 0, 5, "市場狀態", text_color=#808080, text_size=text_size, bgcolor=#000000)
        table.cell(main_dash, 1, 5, market_state, text_color=trend_strength > 0.7 ? col_danger : col_info, text_size=text_size, bgcolor=#000000)
        
        // 信號強度
        signal_power = (trend_strength + (volatility_expansion ? 0.3 : 0)) * 100
        table.cell(main_dash, 0, 6, "信號強度", text_color=#808080, text_size=text_size, bgcolor=#000000)
        table.cell(main_dash, 1, 6, str.tostring(signal_power, "#") + "%", text_color=signal_power > 70 ? col_danger : signal_power > 40 ? col_warning : col_info, text_size=text_size, bgcolor=#000000)
        
        // 活躍層級
        active_levels = str.tostring(cascade_depth) + " 波段, " + str.tostring(array.size(vortex_values)) + " 螺旋"
        table.cell(main_dash, 0, 7, "活躍層級", text_color=#808080, text_size=text_size, bgcolor=#000000)
        table.cell(main_dash, 1, 7, active_levels, text_color=col_primary, text_size=text_size, bgcolor=#000000)

// ========================================
// 漩渦指標面板
// ========================================
if show_vortex_metrics and barstate.islast
    var table vortex_panel = table.new(position.middle_left, 2, 7, bgcolor=#000000, border_color=#333333, border_width=1)
    
    text_size = get_text_size()
    
    table.cell(vortex_panel, 0, 0, "漩渦動態", text_color=col_secondary, text_size=text_size, bgcolor=#000000)
    table.cell(vortex_panel, 1, 0, "", bgcolor=#000000)
    
    // 使用直接變量顯示實際漩渦偏差
    if array.size(fib_periods) > 0
        // F8
        if array.size(fib_periods) > 0
            deviation_0 = ((vortex_8 - close) / close) * 100
            table.cell(vortex_panel, 0, 1, "F" + str.tostring(period_0), text_color=#808080, text_size=text_size, bgcolor=#000000)
            table.cell(vortex_panel, 1, 1, str.tostring(deviation_0, "#.##") + "%", text_color=deviation_0 > 0 ? col_primary : col_danger, text_size=text_size, bgcolor=#000000)
        
        // F13
        if array.size(fib_periods) > 1
            deviation_1 = ((vortex_13 - close) / close) * 100
            table.cell(vortex_panel, 0, 2, "F" + str.tostring(period_1), text_color=#808080, text_size=text_size, bgcolor=#000000)
            table.cell(vortex_panel, 1, 2, str.tostring(deviation_1, "#.##") + "%", text_color=deviation_1 > 0 ? col_primary : col_danger, text_size=text_size, bgcolor=#000000)
        
        // F21
        if array.size(fib_periods) > 2
            deviation_2 = ((vortex_21 - close) / close) * 100
            table.cell(vortex_panel, 0, 3, "F" + str.tostring(period_2), text_color=#808080, text_size=text_size, bgcolor=#000000)
            table.cell(vortex_panel, 1, 3, str.tostring(deviation_2, "#.##") + "%", text_color=deviation_2 > 0 ? col_primary : col_danger, text_size=text_size, bgcolor=#000000)
        
        // F34
        if array.size(fib_periods) > 3
            deviation_3 = ((vortex_34 - close) / close) * 100
            table.cell(vortex_panel, 0, 4, "F" + str.tostring(period_3), text_color=#808080, text_size=text_size, bgcolor=#000000)
            table.cell(vortex_panel, 1, 4, str.tostring(deviation_3, "#.##") + "%", text_color=deviation_3 > 0 ? col_primary : col_danger, text_size=text_size, bgcolor=#000000)
        
        // F55
        if array.size(fib_periods) > 4
            deviation_4 = ((vortex_55 - close) / close) * 100
            table.cell(vortex_panel, 0, 5, "F" + str.tostring(period_4), text_color=#808080, text_size=text_size, bgcolor=#000000)
            table.cell(vortex_panel, 1, 5, str.tostring(deviation_4, "#.##") + "%", text_color=deviation_4 > 0 ? col_primary : col_danger, text_size=text_size, bgcolor=#000000)
    
    // 收斂指標 - 使用直接漩渦值
    vortex_convergence = 0.0
    vortex_conv_state = "中性"
    
    // 直接計算偏差
    dev_0 = math.abs((vortex_8 - close) / close)
    dev_1 = math.abs((vortex_13 - close) / close)
    dev_2 = math.abs((vortex_21 - close) / close)
    dev_3 = math.abs((vortex_34 - close) / close)
    dev_4 = math.abs((vortex_55 - close) / close)
    
    // 尋找最大最小偏差
    max_dev = math.max(dev_0, math.max(dev_1, math.max(dev_2, math.max(dev_3, dev_4))))
    min_dev = math.min(dev_0, math.min(dev_1, math.min(dev_2, math.min(dev_3, dev_4))))
    
    vortex_convergence := (max_dev - min_dev) * 100
    vortex_conv_state := vortex_convergence < 1 ? "緊密" : vortex_convergence < 3 ? "中等" : "寬鬆"
    
    table.cell(vortex_panel, 0, 6, "收斂度", text_color=#808080, text_size=text_size, bgcolor=#000000)
    table.cell(vortex_panel, 1, 6, vortex_conv_state + " (" + str.tostring(vortex_convergence, "#.#") + "%)", text_color=vortex_convergence < 1 ? col_primary : col_danger, text_size=text_size, bgcolor=#000000)

// ========================================
// 分形指標顯示
// ========================================
if barstate.islast
    var table fractal_table = table.new(position.bottom_right, 2, 5, bgcolor=#000000, border_color=#333333, border_width=1)
    
    metric_size = get_text_size()
    
    table.cell(fractal_table, 0, 0, "分形指標", text_color=col_info, text_size=metric_size, bgcolor=#000000)
    table.cell(fractal_table, 1, 0, "", bgcolor=#000000)
    
    // 動態維度顯示
    table.cell(fractal_table, 0, 1, "維度 D", text_color=#808080, text_size=metric_size, bgcolor=#000000)
    table.cell(fractal_table, 1, 1, str.tostring(fractal_dimension, "#.###"), text_color=col_accent, text_size=metric_size, bgcolor=#000000)
    
    // 實際計算的複雜度
    roughness = math.abs(fractal_dimension - 1.5)
    complexity = roughness < 0.2 ? "高" : roughness < 0.5 ? "中等" : "低"
    table.cell(fractal_table, 0, 2, "複雜度", text_color=#808080, text_size=metric_size, bgcolor=#000000)
    table.cell(fractal_table, 1, 2, complexity, text_color=complexity == "高" ? col_danger : col_warning, text_size=metric_size, bgcolor=#000000)
    
    // 基於赫斯特持續性的自相似性
    similarity = math.abs(hurst - 0.5) > 0.2 ? "強" : "弱"
    table.cell(fractal_table, 0, 3, "自相似性", text_color=#808080, text_size=metric_size, bgcolor=#000000)
    table.cell(fractal_table, 1, 3, similarity, text_color=similarity == "強" ? col_primary : col_info, text_size=metric_size, bgcolor=#000000)
    
    // 趨勢質量
    trend_quality = trend_strength > 0.7 ? "極佳" : trend_strength > 0.4 ? "良好" : "差"
    table.cell(fractal_table, 0, 4, "趨勢質量", text_color=#808080, text_size=metric_size, bgcolor=#000000)
    table.cell(fractal_table, 1, 4, trend_quality, text_color=trend_strength > 0.7 ? col_danger : col_warning, text_size=metric_size, bgcolor=#000000)

// ========================================
// 曼德博-斐波那契理論指南
// ========================================
if show_theory_guide and barstate.islast
    var table theory_guide = table.new(position.bottom_left, 1, 11, bgcolor=#000000, border_color=#333333, border_width=1)
    
    // 獲取動態文字大小
    text_size = get_text_size()
    
    table.cell(theory_guide, 0, 0, "曼德博-斐波那契理論", text_color=col_primary, text_size=text_size, bgcolor=#000000)
    table.cell(theory_guide, 0, 1, "", bgcolor=#1a1a1a, height=2)
    table.cell(theory_guide, 0, 2, "曼德博原理:", text_color=col_secondary, text_size=text_size, text_halign=text.align_left, bgcolor=#000000)
    table.cell(theory_guide, 0, 3, "• 市場是分形的 - 模式在所有尺度重複", text_color=#808080, text_size=text_size, text_halign=text.align_left, bgcolor=#000000)
    table.cell(theory_guide, 0, 4, "• 波動在不同時間框架級聯傳導", text_color=#808080, text_size=text_size, text_halign=text.align_left, bgcolor=#000000)
    table.cell(theory_guide, 0, 5, "• 赫斯特H>0.5=趨勢市, H<0.5=均值回歸", text_color=#808080, text_size=text_size, text_halign=text.align_left, bgcolor=#000000)
    table.cell(theory_guide, 0, 6, "", bgcolor=#1a1a1a, height=2)
    table.cell(theory_guide, 0, 7, "斐波那契漩渦:", text_color=col_accent, text_size=text_size, text_halign=text.align_left, bgcolor=#000000)
    table.cell(theory_guide, 0, 8, "• 價格螺旋遵循黃金比率φ=1.618", text_color=#808080, text_size=text_size, text_halign=text.align_left, bgcolor=#000000)
    table.cell(theory_guide, 0, 9, "• 多重斐波週期創建漩渦層", text_color=#808080, text_size=text_size, text_halign=text.align_left, bgcolor=#000000)
    table.cell(theory_guide, 0, 10, "💡 " + (trend_persistence ? "跟隨級聯" : mean_reversion_zone ? "逆轉漩渦" : "等待清晰信號"), text_color=col_warning, text_size=text_size, text_halign=text.align_left, bgcolor=#000000)

// ========================================
// 警報
// ========================================
alertcondition(fractal_signal, "🌀 分形信號", "檢測到分形模式 - 潛在入場點！")
alertcondition(cascade_signal, "🌊 級聯信號", "檢測到級聯模式 - 潛在反轉！")
alertcondition(volatility_expansion, "🌋 波動擴張", "多時間框架波動擴張！")
alertcondition(trend_strength > 0.7, "💪 強趨勢", "市場顯示強方向性運動！")